{"format": 1, "restore": {"D:\\桌面\\PC_Control2\\PC_Ctrl\\PC_Control2.Demo.csproj": {}}, "projects": {"D:\\桌面\\PC_Control2\\PC_Ctrl\\PC_Control2.Demo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\桌面\\PC_Control2\\PC_Ctrl\\PC_Control2.Demo.csproj", "projectName": "PC_Control2.Demo", "projectPath": "D:\\桌面\\PC_Control2\\PC_Ctrl\\PC_Control2.Demo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\桌面\\PC_Control2\\PC_Ctrl\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "HandyControl": {"target": "Package", "version": "[3.4.0, )"}, "LiveChartsCore.SkiaSharpView.WPF": {"target": "Package", "version": "[2.0.0-rc2, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "NodeNetwork": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}