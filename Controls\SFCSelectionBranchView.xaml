<UserControl x:Class="PC_Control2.Demo.Controls.SFCSelectionBranchView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:controls="clr-namespace:PC_Control2.Demo.Controls"
             xmlns:converters="clr-namespace:PC_Control2.Demo.Converters"
             mc:Ignorable="d"
             d:DesignHeight="50" Width="182">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/SFCBlueprintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 布尔值到可见性转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- 使用Canvas精确布局，集中化样式定义 -->
    <Canvas x:Name="MainCanvas" Height="50">

        <!-- 右键菜单 -->
        <Canvas.ContextMenu>
            <ContextMenu Style="{StaticResource BlueprintContextMenuStyle}">
                <MenuItem Header="切换分支类型"
                         Command="{Binding ToggleBranchTypeCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🔄"/>
                <MenuItem Header="设为汇聚分支"
                         IsCheckable="True"
                         IsChecked="{Binding IsConvergence}"
                         Command="{Binding ToggleConvergenceCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚡"/>
                <Separator/>
                <MenuItem Header="插入新分支"
                          Command="{Binding DataContext.InsertSelectionBranchFromAnyPartCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          Style="{StaticResource BlueprintMenuItemStyle}"
                          Icon="➕"/>
                <MenuItem Header="插入兄弟分支(原方法)"
                          Command="{Binding DataContext.InsertSiblingBranchCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                          CommandParameter="{Binding}"
                          Style="{StaticResource BlueprintMenuItemStyle}"
                          Icon="🔗"/>
                <Separator/>
                <MenuItem Header="复制分支"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="📋"/>
                <MenuItem Header="删除分支"
                         Command="{Binding DeleteCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="🗑"/>
                <Separator/>
                <MenuItem Header="属性"
                         Command="{Binding EditPropertiesCommand}"
                         Style="{StaticResource BlueprintMenuItemStyle}"
                         Icon="⚙"/>
            </ContextMenu>
        </Canvas.ContextMenu>

        <!-- 左侧分支交互层 -->
        <Rectangle x:Name="LeftBranchInteractionLayer"
                   Width="50"
                   Height="50"
                   Canvas.Left="-1"
                   Canvas.Top="0"
                   Fill="Transparent"
                   Cursor="Hand"
                   Panel.ZIndex="100"
                   MouseLeftButtonDown="LeftClickArea_MouseLeftButtonDown" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Rectangle.Style>
                <Style TargetType="Rectangle">
                    <Style.Triggers>
                        <!-- 选中状态视觉反馈 -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedBorderBrush}"/>
                            <Setter Property="StrokeThickness" Value="2"/>
                            <Setter Property="StrokeDashArray" Value="3,2"/>
                            <Setter Property="Opacity" Value="0.3"/>
                        </DataTrigger>
                        <!-- 悬停状态视觉反馈 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Stroke" Value="#FF00FF00"/>
                            <Setter Property="StrokeThickness" Value="1"/>
                            <Setter Property="StrokeDashArray" Value="2,1"/>
                            <Setter Property="Opacity" Value="0.2"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 右侧分支交互层 -->
        <Rectangle x:Name="RightBranchInteractionLayer"
                   Width="133"
                   Height="70"
                   Canvas.Left="49"
                   Canvas.Top="0"
                   Fill="Transparent"
                   Cursor="Hand"
                   Panel.ZIndex="100"
                   MouseLeftButtonDown="RightClickArea_MouseLeftButtonDown" HorizontalAlignment="Left" VerticalAlignment="Center">
            <Rectangle.Style>
                <Style TargetType="Rectangle">
                    <Style.Triggers>
                        <!-- 选中状态视觉反馈 -->
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedBorderBrush}"/>
                            <Setter Property="StrokeThickness" Value="2"/>
                            <Setter Property="StrokeDashArray" Value="3,2"/>
                            <Setter Property="Opacity" Value="0.3"/>
                        </DataTrigger>
                        <!-- 悬停状态视觉反馈 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Stroke" Value="#FF00FF00"/>
                            <Setter Property="StrokeThickness" Value="1"/>
                            <Setter Property="StrokeDashArray" Value="2,1"/>
                            <Setter Property="Opacity" Value="0.2"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 左侧竖直连接线 -->
        <Line x:Name="LeftVerticalLine"
              X1="20" Y1="15" X2="20" Y2="40"
              Canvas.Left="1" Canvas.Top="-8"
              IsHitTestVisible="False"
              HorizontalAlignment="Left" VerticalAlignment="Center">
            <Line.Style>
                <Style TargetType="Line" BasedOn="{StaticResource BlueprintConnectionPathStyle}">
                    <Style.Triggers>
                        <!-- 根据ViewType控制可见性：只有Initial类型才显示左侧竖直连接线 -->
                        <DataTrigger Binding="{Binding ViewType}" Value="Subsequent">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="StrokeThickness" Value="3"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Line.Style>
        </Line>

        <!-- 主横线 - 选择分支的连接横线 -->
        <Line x:Name="MainHorizontalLine"
              X1="-103" Y1="25" X2="44" Y2="25"
              Canvas.Left="124" Canvas.Top="10"
              IsHitTestVisible="False"
              HorizontalAlignment="Left" VerticalAlignment="Center">
            <Line.Style>
                <Style TargetType="Line" BasedOn="{StaticResource BlueprintConnectionPathStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="StrokeThickness" Value="3"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Line.Style>
        </Line>


        <!-- 左侧竖线上端连接点 -->
        <controls:SFCConnectPoint x:Name="LeftTopConnectPoint"
            Canvas.Left="16" HorizontalAlignment="Left" VerticalAlignment="Center" Canvas.Top="2"
            IsHitTestVisible="True" PointType="Input" Index="0" ElementId="{Binding Id}" ElementType="Branch">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="controls:SFCConnectPoint">
                    <Style.Triggers>
                        <!-- 根据ViewType控制可见性：只有Initial类型才显示左侧上端连接点 -->
                        <DataTrigger Binding="{Binding ViewType}" Value="Subsequent">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 左侧竖线下端连接点 -->
        <controls:SFCConnectPoint x:Name="LeftBottomConnectPoint"
            Canvas.Left="16" HorizontalAlignment="Left" VerticalAlignment="Center" Canvas.Top="30"
            IsHitTestVisible="True" PointType="Output" Index="1" ElementId="{Binding Id}" ElementType="Branch">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="controls:SFCConnectPoint">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 右侧竖直连接线 -->
        <Rectangle x:Name="BottomConnectionLine"
            Width="3"
            Height="31"
            Canvas.Left="165.5" HorizontalAlignment="Left" VerticalAlignment="Center" Canvas.Top="35"
            IsHitTestVisible="False">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 转换条件的连接虚线 -->
        <Line x:Name="MainTransitionLine"
            X1="29" Y1="15" X2="60" Y2="15"
            StrokeDashArray="1,1"
            Canvas.Left="107" Canvas.Top="36" HorizontalAlignment="Left" VerticalAlignment="Center"
            IsHitTestVisible="False">
            <Line.Style>
                <Style TargetType="{x:Type Line}" BasedOn="{StaticResource BlueprintConnectionPathStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="StrokeThickness" Value="3"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Line.Style>
        </Line>

        <!-- 转换条件的十字横线 -->
        <Line x:Name="MainTransitionLine2"
            X1="55" Y1="15" X2="65" Y2="15"
            StrokeThickness="3"
            Canvas.Left="107" Canvas.Top="36" HorizontalAlignment="Left" VerticalAlignment="Center"
            IsHitTestVisible="False">
            <Line.Style>
                <Style TargetType="{x:Type Line}" BasedOn="{StaticResource BlueprintConnectionPathStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Stroke" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="StrokeThickness" Value="3"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Line.Style>
        </Line>

        <!-- 转换条件的触点左侧竖线 -->
        <Rectangle x:Name="BottomConnectionLine2"
            Width="2"
            Height="12"
            Canvas.Left="128" Canvas.Top="44" HorizontalAlignment="Left" VerticalAlignment="Center"
            IsHitTestVisible="False">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 转换条件的触点右侧竖线 -->
        <Rectangle x:Name="BottomConnectionLine3"
            Width="2"
            Height="8"
            Canvas.Left="135" Canvas.Top="47" HorizontalAlignment="Left" VerticalAlignment="Center"
            IsHitTestVisible="False">
            <Rectangle.Style>
                <Style TargetType="{x:Type Rectangle}" BasedOn="{StaticResource BlueprintConnectionLineStyle}">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Fill" Value="{StaticResource BlueprintSelectedConnectionLine}"/>
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Rectangle.Style>
        </Rectangle>

        <!-- 右侧竖线上端连接点 -->
        <controls:SFCConnectPoint x:Name="TopConnectPoint1"
            Canvas.Left="162" HorizontalAlignment="Left" VerticalAlignment="Center" Canvas.Top="30"
            IsHitTestVisible="True" PointType="Input" Index="2" ElementId="{Binding Id}" ElementType="Branch">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="controls:SFCConnectPoint">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 右侧竖线下端连接点 -->
        <controls:SFCConnectPoint x:Name="TopConnectPoint2"
            Canvas.Left="162" Canvas.Top="61" HorizontalAlignment="Left" VerticalAlignment="Center"
            IsHitTestVisible="True" PointType="Output" Index="3" ElementId="{Binding Id}" ElementType="Branch">
            <controls:SFCConnectPoint.Style>
                <Style TargetType="controls:SFCConnectPoint">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsInitialStep}" Value="True">
                            <Setter Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding IsSelected}" Value="True">
                            <Setter Property="Effect" Value="{StaticResource BlueprintGlowEffect}"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </controls:SFCConnectPoint.Style>
        </controls:SFCConnectPoint>

        <!-- 汇聚标识 -->

        <!-- 选择分支内部转换条件标识符 - 显示在右侧转换条件的右侧 -->
        <Border x:Name="InternalTransitionIdentifier"
                Canvas.Left="176" Canvas.Top="44"
                Background="#FF2D2D30"
                BorderBrush="#FF8A7CA8"
                BorderThickness="1"
                CornerRadius="2"
                Padding="3,1"
                Visibility="{Binding InternalTransitionHasLabel, Converter={StaticResource BooleanToVisibilityConverter}}"
                IsHitTestVisible="False">
            <TextBlock Text="{Binding InternalTransitionDisplayText}"
                       Foreground="#FFCCCCCC"
                       FontSize="9"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
        </Border>
    </Canvas>
</UserControl>
