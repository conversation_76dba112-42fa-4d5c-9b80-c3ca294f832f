using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using PC_Control2.Demo.ViewModels;
using PC_Control2.Demo.Models;
using System.Windows.Media;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCBranchView.xaml 的交互逻辑
    /// </summary>
    public partial class SFCBranchView : UserControl
    {
        public SFCBranchViewModel? ViewModel => DataContext as SFCBranchViewModel;

        public SFCBranchView()
        {
            InitializeComponent();
            
            // 设置可拖拽
            this.MouseLeftButtonDown += OnMouseLeftButtonDown;
            this.MouseLeftButtonUp += OnMouseLeftButtonUp;
            this.MouseMove += OnMouseMove;
            this.MouseDoubleClick += OnMouseDoubleClick;
            
            // 设置工具提示
            ToolTip = new ToolTip
            {
                Content = "分支: 双击切换类型，右键查看更多选项"
            };

            // 在DataContext变化时更新视觉效果
            DataContextChanged += SFCBranchView_DataContextChanged;
        }

        private bool _isDragging = false;
        private Point _dragStartPoint;
        private Point _dragStartPosition; // 拖拽开始时的元素位置

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (ViewModel == null) return;

            // 对于选择分支，让内层的SFCSelectionBranchView处理拖拽
            if (ViewModel.BranchType == Models.SFCBranchType.Selection)
            {
                // 只处理选中，不处理拖拽
                ViewModel.SelectCommand?.Execute(null);
                return;
            }

            // 选中分支
            ViewModel.SelectCommand?.Execute(null);

            // 开始拖拽（仅对非选择分支）
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            _dragStartPosition = ViewModel.Position; // 记录拖拽开始时的位置
            this.CaptureMouse();

            // 不设置e.Handled = true，让事件继续冒泡到SFCCanvas
        }

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            // 对于选择分支，让内层的SFCSelectionBranchView处理拖拽
            if (ViewModel?.BranchType == Models.SFCBranchType.Selection)
            {
                return;
            }

            if (_isDragging && ViewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新分支位置（基于拖拽开始时的初始位置，避免累积误差）
                var newPosition = new Point(
                    _dragStartPosition.X + deltaX,
                    _dragStartPosition.Y + deltaY);

                // System.Diagnostics.Debug.WriteLine($"[SFCBranchView] 拖动更新位置: {ViewModel.Id} -> {newPosition}");
                ViewModel.Position = newPosition;

                e.Handled = true;
            }
        }

        /// <summary>
        /// 移动与选择分支关联的转换条件，确保整体移动
        /// </summary>
        private void MoveAssociatedTransitions(double deltaX, double deltaY)
        {
            if (ViewModel == null) return;

            // 获取父级SFC视图模型
            var parentCanvas = this.FindParent<Controls.SFCCanvas>();
            if (parentCanvas?.DataContext is ViewModels.EnhancedSFCViewModel sfcViewModel)
            {
                // 查找与此分支关联的右侧转换条件
                // 根据命名约定或位置关系找到关联的转换条件
                var associatedTransitions = sfcViewModel.TransitionViewModels
                    .Where(t => IsAssociatedWithBranch(t, ViewModel))
                    .ToList();

                foreach (var transition in associatedTransitions)
                {
                    transition.Position = new Point(
                        transition.Position.X + deltaX,
                        transition.Position.Y + deltaY);
                }
            }
        }

        /// <summary>
        /// 判断转换条件是否与分支关联
        /// </summary>
        private bool IsAssociatedWithBranch(ViewModels.SFCTransitionViewModel transition, ViewModels.SFCBranchViewModel branch)
        {
            // 基于位置关系判断：右侧转换条件应该在分支右侧且Y坐标相近
            var branchRight = branch.Position.X + branch.Size.Width;
            var isOnRight = transition.Position.X > branchRight &&
                           transition.Position.X < branchRight + 150; // 150像素范围内
            var isSameLevel = Math.Abs(transition.Position.Y - branch.Position.Y) < 30; // Y坐标差小于30像素

            return isOnRight && isSameLevel;
        }

        private void OnMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ViewModel == null) return;

            // 双击切换分支类型
            ViewModel.ToggleBranchTypeCommand.Execute(null);
            
            e.Handled = true;
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (ViewModel == null) return;

            switch (e.Key)
            {
                case Key.Delete:
                    ViewModel.DeleteCommand?.Execute(null);
                    e.Handled = true;
                    break;
                case Key.F2:
                    ViewModel.EditPropertiesCommand?.Execute(null);
                    e.Handled = true;
                    break;
                case Key.Space:
                    // 空格键切换分支类型
                    ViewModel.ToggleBranchTypeCommand.Execute(null);
                    e.Handled = true;
                    break;
                case Key.C:
                    if (Keyboard.Modifiers == ModifierKeys.Control)
                    {
                        // Ctrl+C 切换汇聚状态
                        ViewModel.ToggleConvergenceCommand.Execute(null);
                        e.Handled = true;
                    }
                    break;
            }

            base.OnKeyDown(e);
        }

        protected override void OnRender(System.Windows.Media.DrawingContext drawingContext)
        {
            base.OnRender(drawingContext);

            // 根据分支类型更新视觉效果
            UpdateVisualAppearance();
        }

        private void UpdateVisualAppearance()
        {
            if (ViewModel == null) return;

            // 不再需要更新标记可见性，因为使用Grid的Visibility属性绑定来控制
            // 这些元素已在XAML中通过Visibility绑定处理
        }

        private void SFCBranchView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 数据上下文变化时的处理
            // 现在使用连接线而非连接点，不需要额外处理
        }
        
        // 西门子博图风格不再使用连接点，而是使用连接线
    }
}

/// <summary>
/// 扩展方法类
/// </summary>
public static class VisualTreeExtensions
{
    /// <summary>
    /// 查找指定类型的父级控件
    /// </summary>
    public static T? FindParent<T>(this DependencyObject child) where T : DependencyObject
    {
        DependencyObject parentObject = VisualTreeHelper.GetParent(child);

        if (parentObject == null) return null;

        if (parentObject is T parent)
            return parent;

        return FindParent<T>(parentObject);
    }
}
