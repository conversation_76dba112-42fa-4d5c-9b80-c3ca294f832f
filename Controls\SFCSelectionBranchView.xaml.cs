using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using PC_Control2.Demo.ViewModels;
using PC_Control2.Demo.Models;
using System.Linq;

namespace PC_Control2.Demo.Controls
{
    /// <summary>
    /// SFCSelectionBranchView.xaml 的交互逻辑
    /// </summary>
    public partial class SFCSelectionBranchView : UserControl
    {
        #region 私有字段

        private SFCConnectPoint? _leftTopConnectPoint;
        private SFCConnectPoint? _leftBottomConnectPoint;
        private SFCConnectPoint? _rightTopConnectPoint;
        private SFCConnectPoint? _rightBottomConnectPoint;
        private SFCBranchViewModel? _viewModel;

        // 拖拽相关
        private bool _isDragging = false;
        private Point _dragStartPoint;
        private Point _dragStartPosition; // 拖拽开始时的元素位置

        #endregion

        #region 属性

        /// <summary>
        /// ViewModel属性
        /// </summary>
        public SFCBranchViewModel? ViewModel => _viewModel;

        #endregion

        #region 依赖属性
        
        /// <summary>
        /// 连接交互管理器依赖属性
        /// </summary>
        public static readonly DependencyProperty InteractionManagerProperty =
            DependencyProperty.Register("InteractionManager", typeof(SFCConnectPointInteractionManager), typeof(SFCSelectionBranchView),
                new PropertyMetadata(null, OnInteractionManagerChanged));
                
        #endregion
        
        #region 公共属性
        
        /// <summary>
        /// 连接交互管理器
        /// </summary>
        public SFCConnectPointInteractionManager? InteractionManager
        {
            get { return (SFCConnectPointInteractionManager?)GetValue(InteractionManagerProperty); }
            set { SetValue(InteractionManagerProperty, value); }
        }
        
        #endregion
        
        #region 构造函数
        
        public SFCSelectionBranchView()
        {
            InitializeComponent();

            // 数据上下文变化事件
            DataContextChanged += OnDataContextChanged;

            // 控件加载完成事件
            Loaded += OnLoaded;

            // 设置鼠标事件（用于拖拽）
            this.MouseLeftButtonUp += OnMouseLeftButtonUp;
            this.MouseMove += OnMouseMove;
        }
        
        #endregion
        
        #region 事件处理
        
        /// <summary>
        /// 数据上下文变化处理
        /// </summary>
        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            _viewModel = e.NewValue as SFCBranchViewModel;
            
            // 初始化连接点
            if (_viewModel != null)
            {
                InitializeConnectPoints();
            }
        }
        
        /// <summary>
        /// 控件加载完成处理
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 初始化连接点
            InitializeConnectPoints();
        }
        
        /// <summary>
        /// 交互管理器变化处理
        /// </summary>
        private static void OnInteractionManagerChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SFCSelectionBranchView branchView)
            {
                branchView.UpdateConnectPointsInteractionManager();
            }
        }
        
        /// <summary>
        /// 左侧区域鼠标点击处理
        /// </summary>
        private void LeftClickArea_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 选中分支左侧部分
            _viewModel.SelectCommand?.Execute(_viewModel);
            _viewModel.SelectedPart = "Left";

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            _dragStartPosition = _viewModel.Position; // 记录拖拽开始时的位置
            this.CaptureMouse();

            System.Diagnostics.Debug.WriteLine($"[SFCSelectionBranchView] 左侧区域选中并开始拖拽: {_viewModel.Id}");

            // 不设置e.Handled = true，让事件继续冒泡以支持其他功能
        }

        /// <summary>
        /// 右侧区域鼠标点击处理
        /// </summary>
        private void RightClickArea_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (_viewModel == null) return;

            // 选中分支右侧部分
            _viewModel.SelectCommand?.Execute(_viewModel);
            _viewModel.SelectedPart = "Right";

            // 开始拖拽
            _isDragging = true;
            _dragStartPoint = e.GetPosition(this);
            _dragStartPosition = _viewModel.Position; // 记录拖拽开始时的位置
            this.CaptureMouse();

            System.Diagnostics.Debug.WriteLine($"[SFCSelectionBranchView] 右侧区域选中并开始拖拽: {_viewModel.Id}");

            // 不设置e.Handled = true，让事件继续冒泡以支持其他功能
        }
        
        #endregion
        
        #region 私有方法
        
        /// <summary>
        /// 初始化连接点
        /// </summary>
        private void InitializeConnectPoints()
        {
            if (_viewModel == null)
            {
                System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] ViewModel为空，跳过初始化");
                return;
            }

            // 防止重复初始化
            if (_leftTopConnectPoint != null && _leftTopConnectPoint.ElementId == _viewModel.Id)
            {
                System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] 连接点已初始化，跳过重复初始化: {_viewModel.Id}");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] 开始初始化连接点，ViewModel.Id: {_viewModel.Id}");

            // 查找连接点控件
            _leftTopConnectPoint = LeftTopConnectPoint as SFCConnectPoint;
            _leftBottomConnectPoint = LeftBottomConnectPoint as SFCConnectPoint;
            _rightTopConnectPoint = TopConnectPoint1 as SFCConnectPoint;
            _rightBottomConnectPoint = TopConnectPoint2 as SFCConnectPoint;

            System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] XAML连接点查找结果: LeftTop={_leftTopConnectPoint != null}, LeftBottom={_leftBottomConnectPoint != null}, RightTop={_rightTopConnectPoint != null}, RightBottom={_rightBottomConnectPoint != null}");

            // 检查并修复XAML连接点的ElementId绑定
            if (_leftTopConnectPoint != null)
            {
                System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] LeftTop连接点ElementId: '{_leftTopConnectPoint.ElementId}', 期望: '{_viewModel.Id}'");

                // 手动设置ElementId，因为XAML绑定没有生效
                _leftTopConnectPoint.ElementId = _viewModel.Id;
                _leftTopConnectPoint.ElementType = SFCElementType.Branch;
                _leftTopConnectPoint.PointType = ConnectPointType.Input;
                _leftTopConnectPoint.Index = 0;

                System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] ✅ 已修复LeftTop连接点ElementId: '{_leftTopConnectPoint.ElementId}'");
            }
            
            // 修复其他连接点的ElementId
            if (_leftBottomConnectPoint != null)
            {
                _leftBottomConnectPoint.ElementId = _viewModel.Id;
                _leftBottomConnectPoint.ElementType = SFCElementType.Branch;
                _leftBottomConnectPoint.PointType = ConnectPointType.Output;
                _leftBottomConnectPoint.Index = 1;
            }

            if (_rightTopConnectPoint != null)
            {
                _rightTopConnectPoint.ElementId = _viewModel.Id;
                _rightTopConnectPoint.ElementType = SFCElementType.Branch;
                _rightTopConnectPoint.PointType = ConnectPointType.Input;
                _rightTopConnectPoint.Index = 2;
            }

            if (_rightBottomConnectPoint != null)
            {
                _rightBottomConnectPoint.ElementId = _viewModel.Id;
                _rightBottomConnectPoint.ElementType = SFCElementType.Branch;
                _rightBottomConnectPoint.PointType = ConnectPointType.Output;
                _rightBottomConnectPoint.Index = 3;
            }

            // 如果连接点控件不存在，则创建（这种情况应该不会发生了）
            if (_leftTopConnectPoint == null)
            {
                System.Diagnostics.Debug.WriteLine($"[InitializeConnectPoints] ❌ 警告：LeftTop连接点不存在，创建新的");
                _leftTopConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.Branch,
                    PointType = ConnectPointType.Input,
                    Index = 0
                };
                
                // 替换原有连接点
                if (LeftTopConnectPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(LeftTopConnectPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _leftTopConnectPoint;
                        Canvas.SetLeft(_leftTopConnectPoint, Canvas.GetLeft(LeftTopConnectPoint));
                        Canvas.SetTop(_leftTopConnectPoint, Canvas.GetTop(LeftTopConnectPoint));
                    }
                }
            }
            
            if (_leftBottomConnectPoint == null)
            {
                _leftBottomConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.Branch,
                    PointType = ConnectPointType.Output,
                    Index = 1
                };
                
                // 替换原有连接点
                if (LeftBottomConnectPoint != null)
                {
                    int index = MainCanvas.Children.IndexOf(LeftBottomConnectPoint);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _leftBottomConnectPoint;
                        Canvas.SetLeft(_leftBottomConnectPoint, Canvas.GetLeft(LeftBottomConnectPoint));
                        Canvas.SetTop(_leftBottomConnectPoint, Canvas.GetTop(LeftBottomConnectPoint));
                    }
                }
            }
            
            if (_rightTopConnectPoint == null)
            {
                _rightTopConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.Branch,
                    PointType = ConnectPointType.Input,
                    Index = 2
                };
                
                // 替换原有连接点
                if (TopConnectPoint1 != null)
                {
                    int index = MainCanvas.Children.IndexOf(TopConnectPoint1);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _rightTopConnectPoint;
                        Canvas.SetLeft(_rightTopConnectPoint, Canvas.GetLeft(TopConnectPoint1));
                        Canvas.SetTop(_rightTopConnectPoint, Canvas.GetTop(TopConnectPoint1));
                    }
                }
            }
            
            if (_rightBottomConnectPoint == null)
            {
                _rightBottomConnectPoint = new SFCConnectPoint
                {
                    ElementId = _viewModel.Id,
                    ElementType = SFCElementType.Branch,
                    PointType = ConnectPointType.Output,
                    Index = 3
                };
                
                // 替换原有连接点
                if (TopConnectPoint2 != null)
                {
                    int index = MainCanvas.Children.IndexOf(TopConnectPoint2);
                    if (index >= 0)
                    {
                        MainCanvas.Children[index] = _rightBottomConnectPoint;
                        Canvas.SetLeft(_rightBottomConnectPoint, Canvas.GetLeft(TopConnectPoint2));
                        Canvas.SetTop(_rightBottomConnectPoint, Canvas.GetTop(TopConnectPoint2));
                    }
                }
            }
            
            // 设置连接点适配器
            if (_viewModel.ConnectPointAdapters != null && _viewModel.ConnectPointAdapters.Count >= 4)
            {
                if (_leftTopConnectPoint != null)
                {
                    _leftTopConnectPoint.Adapter = _viewModel.ConnectPointAdapters[0];
                }
                
                if (_leftBottomConnectPoint != null)
                {
                    _leftBottomConnectPoint.Adapter = _viewModel.ConnectPointAdapters[1];
                }
                
                if (_rightTopConnectPoint != null)
                {
                    _rightTopConnectPoint.Adapter = _viewModel.ConnectPointAdapters[2];
                }
                
                if (_rightBottomConnectPoint != null)
                {
                    _rightBottomConnectPoint.Adapter = _viewModel.ConnectPointAdapters[3];
                }
            }
            
            // 更新交互管理器
            UpdateConnectPointsInteractionManager();
        }
        
        /// <summary>
        /// 更新连接点的交互管理器
        /// </summary>
        private void UpdateConnectPointsInteractionManager()
        {
            if (_leftTopConnectPoint != null)
            {
                _leftTopConnectPoint.InteractionManager = InteractionManager;
            }
            
            if (_leftBottomConnectPoint != null)
            {
                _leftBottomConnectPoint.InteractionManager = InteractionManager;
            }
            
            if (_rightTopConnectPoint != null)
            {
                _rightTopConnectPoint.InteractionManager = InteractionManager;
            }
            
            if (_rightBottomConnectPoint != null)
            {
                _rightBottomConnectPoint.InteractionManager = InteractionManager;
            }
        }

        #endregion

        #region 拖拽功能

        private void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging)
            {
                _isDragging = false;
                this.ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        private void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isDragging && ViewModel != null && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(this);
                var deltaX = currentPosition.X - _dragStartPoint.X;
                var deltaY = currentPosition.Y - _dragStartPoint.Y;

                // 更新选择分支位置（基于拖拽开始时的初始位置，避免累积误差）
                var newPosition = new Point(
                    _dragStartPosition.X + deltaX,
                    _dragStartPosition.Y + deltaY);

                // 减少调试输出频率，避免影响性能
                if (Math.Abs(deltaX) > 2 || Math.Abs(deltaY) > 2)
                {
                    System.Diagnostics.Debug.WriteLine($"[SFCSelectionBranchView] 拖动更新位置: {ViewModel.Id} -> {newPosition}");
                }

                ViewModel.Position = newPosition;
                e.Handled = true;
            }
        }

        #endregion
    }
}
